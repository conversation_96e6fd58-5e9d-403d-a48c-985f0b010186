<?php
/**
* Template Name: Kontakt
 */

get_header(); ?>

	

	<div class="mu-page-header" style="background-image: url('/wp-content/uploads/2021/09/header_kontakt.png');">
		<div class="container">
			<div class="row">
				<div class="col-sm-12">
					<p class="mu-page-header--pagename">Muchowiecka apartments</p>
					<h1>Kontakt</h1>
				</div>
			</div>
		</div>
	</div>
	
	<div class="container mu-about-content">
		<div class="mu-contact-content-container mt-5">
			<div class="mu-contact-content-container--column">
				<?php if( have_rows('sekcja_-_skontaktuj_sie_z_nami') ): 
				while( have_rows('sekcja_-_skontaktuj_sie_z_nami') ): the_row(); 
					$podtytul = get_sub_field('podtytul'); 
					$tytul = get_sub_field('tytul'); 
					$tekst = get_sub_field('tekst'); 
					$przycisk = get_sub_field('przycisk'); ?>
					<?php if($podtytul): ?>
						<p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
					<?php endif; ?>
					<?php if($tytul): ?>
						<h2 class="mu-heading"><?php echo $tytul; ?></h2>
					<?php endif; ?>
					<div class="mu-light-content mt-5">
						<?php if($tekst): 
							echo $tekst; 
						endif; ?>
						<?php if($przycisk): ?>
							<a class="mu-primary-btn" target="<?php echo $przycisk['target']; ?>" href="<?php echo $przycisk['url']; ?>"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
						<?php endif; ?>
					</div>
				<?php endwhile;
				endif; ?>
			
			</div>
			<div class="mu-contact-content-container--column mu-dark-content">
				<?php echo do_shortcode('[contact-form-7 id="23" title="Formularz kontaktowy"]'); ?>
			</div>
		</div>
		<div class="mu-contact-content-container mt-5">
			<div class="mu-contact-content-container--column">
				<?php if( have_rows('sekcja_-_dane_firmy') ): 
					while( have_rows('sekcja_-_dane_firmy') ): the_row(); 
						$tytul = get_sub_field('tytul'); 
						$tekst = get_sub_field('tekst');

						if($tytul): ?>
							<h2 class="mu-heading mt-5">Dane firmy</h2>
						<?php endif; ?>
						<?php if($tekst): ?>
						<div class="mu-contact-content-container-column--data">
							<?php echo $tekst; ?>
						</div>
						<?php endif; ?>
						<?php if( have_rows('kontakty') ):
							while( have_rows('kontakty') ) : the_row();
								echo '<div class="mu-about-contacts-row">';
									$image =  get_sub_field('zdjecie');
									if( $image ): 
										$alt = $image['alt'];
										$size = 'thumbnail';
										$thumb = $image['sizes'][ $size ];
									?>
									<img class="mu-about-contacts-photo" src="<?php echo esc_url($thumb); ?>" alt="<?php echo esc_attr($alt); ?>" />

									<?php endif; ?>
									<div class="mu-about-contacts-text">
										<?php echo get_sub_field('tekst'); ?>
									</div>
								</div>
								<?php
							endwhile;
						endif; ?>
					<?php endwhile;
				endif; ?>
			</div>
		</div>
	</div>
		
<?php
get_footer();
