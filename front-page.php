<?php
/**
* Template Name: Strona główna
 */

get_header(); ?>

<?php if( have_rows('sekcja_-_slider') ): 
        while( have_rows('sekcja_-_slider') ): the_row(); ?>
            <div class="mu-page-header-carousel">
                <?php if( have_rows('slajd') ): 
                    while( have_rows('slajd') ): the_row(); 
                        $zdjecie = get_sub_field('zdjecie');
                        $podtytul = get_sub_field('podtytul');
                        $tytul = get_sub_field('tytul');
                        $przycisk = get_sub_field('przycisk'); ?>
                    <div>
                        <div class="mu-page-header" style="background-image: url('<?php echo $zdjecie; ?>');">
                            <div class="container">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="mu-homepage-header">
                                            <?php if($podtytul): ?>
                                                <p class="mu-homepage-header--subtitle">OFERTA SPECJALNA</p>
                                            <?php endif; ?>
                                            <?php if($tytul): ?>
                                                <h1><?php echo $tytul; ?></h1>
                                            <?php endif; ?>
                                            <?php if($przycisk): ?>
                                                <a href="<?php echo $przycisk['url']; ?>" target="<?php echo $przycisk['target']; ?>" class="mu-homepage-header--btn"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endwhile;
                endif; ?>
            </div>
        <?php endwhile;
    endif; ?>

    <div class="container mu-about-content">

        <div class="container mu-home-about-content">
            <div class="row">
                <div class="col-lg-4">
                    <?php if( have_rows('sekcja_-_dlaczego_my') ): 
                        while( have_rows('sekcja_-_dlaczego_my') ): the_row(); 
                            $podtytul = get_sub_field('podtytul');
                            $tytul = get_sub_field('tytul');
                            $tekst = get_sub_field('tekst');
                            $przycisk = get_sub_field('przycisk');
                            $zdjecie = get_sub_field('zdjecie');
                        ?>
                        <?php if($podtytul): ?>
                            <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
                        <?php endif; ?>
                        <?php if($tytul): ?>
                            <h1 class="mu-heading"><?php echo $tytul; ?></h1>
                        <?php endif; ?>
                        <div class="mu-light-content mt-5">
                            <?php if($tekst):
                                echo $tekst;
                            endif; ?>
                            <?php if($przycisk): ?>
                                <a target="<?php echo $przycisk['target'];?>" class="mu-primary-btn mu-primary-btn-color-main" href="<?php echo $przycisk['url'];?>"><?php echo $przycisk['title'];?> <i class="fas fa-long-arrow-alt-right"></i></a>
                            <?php endif; ?>
                        </div>
                        </div>
                        <div class="col-lg-8">
                            <?php if($zdjecie): ?>
                                <img alt="" class="mu-home-about-content--image" src="<?php echo $zdjecie; ?>">
                            <?php endif; ?>
                        </div>
                        <?php endwhile;
                    endif; ?>
            </div>
        </div>
    </div>
    <div class="mu-homepage-our-buildings" style="background-image: url('/wp-content/uploads/2021/09/nasze_budynki.png');">
        <div class="container">
            <div class="row">
                <?php if( have_rows('sekcja_-_nasze_budynki') ): 
                        while( have_rows('sekcja_-_nasze_budynki') ): the_row(); 
                            $tytul = get_sub_field('tytul');
                            $tekst = get_sub_field('tekst');
                            $przycisk = get_sub_field('przycisk');
                        ?>
                        <div class="col-lg-6">
                            <?php if($tytul): ?>
                                <h2 class="mu-heading">Nasze budynki</h2>
                            <?php endif; ?>
                            <?php if($tekst): ?>
                                <div><?php echo $tekst; ?></div>
                            <?php endif; ?>
                            <?php if($przycisk): ?>
                                <a class="mu-primary-btn mu-primary-btn-color-main mu-btn-right" target="<?php echo $przycisk['target']; ?>" href="<?php echo $przycisk['url']; ?>"><?php echo $przycisk['title']; ?></a>
                            <?php endif; ?>
                        </div>
                        <div class="col-lg-6">
                            <?php if( have_rows('pasek_postepu') ): 
                                while( have_rows('pasek_postepu') ): the_row(); 
                                    $tytul = get_sub_field('tytul');
                                    $wartosc_procentowa = get_sub_field('wartosc_procentowa');
                                    $etykieta = get_sub_field('etykieta');
                                ?>
                                <div class="mu-progress-item">
                                    <?php if($tytul): ?>
                                        <label><?php echo $tytul; ?></label>
                                    <?php endif; ?>
                                    <?php if($wartosc_procentowa): ?>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" aria-valuenow="<?php echo $wartosc_procentowa; ?>" aria-valuemin="0" aria-valuemax="100" style="width: <?php echo $wartosc_procentowa; ?>%;">
                                            <?php if($etykieta): ?>
                                                <span><?php echo $etykieta; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endwhile;
                            endif; ?>
                        </div>
                    <?php endwhile;
                    endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php if( have_rows('sekcja_-_liczniki') ): 
            while( have_rows('sekcja_-_liczniki') ): the_row();  ?>
                <div class="container mu-homepage-under-progress-counters-container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mu-homepage-under-progress-counters">
                                <?php if( have_rows('licznik') ): 
                                    while( have_rows('licznik') ): the_row(); 
                                        $liczba = get_sub_field('liczba');
                                        $tytul = get_sub_field('tytul');
                                        $podtytul = get_sub_field('podtytul'); ?>
                                        <div class="mu-under-progress-counter">
                                            <?php if($liczba): ?>
                                                <span class="mu-under-progress-counter--count"><?php echo $liczba; ?></span>
                                            <?php endif; ?>
                                            <?php if($tytul): ?>
                                                <span class="mu-under-progress-counter--title"><?php echo $tytul; ?></span>
                                            <?php endif; ?>
                                            <?php if($podtytul): ?>
                                                <span class="mu-under-progress-counter--object"><?php echo $podtytul; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endwhile;
                                endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
        <?php endwhile;
    endif; ?>
    
    <?php if( have_rows('sekcja_-_slider_ofert') ): 
            while( have_rows('sekcja_-_slider_ofert') ): the_row();  ?>
            <div class="mu-buildings-slider-container">
                <div class="mu-buildings-slider-carousel">
                <?php if( have_rows('nieruchomosc') ): 
                    while( have_rows('nieruchomosc') ): the_row();
                        $zdjecie = get_sub_field('zdjecie');
                        $podtytul = get_sub_field('podtytul');
                        $tytul = get_sub_field('tytul');
                        $przycisk = get_sub_field('przycisk');
                    ?>
                    <div>
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-12 mu-buildings-slider-content">
                                    <?php if($podtytul): ?>
                                        <p class="mu-sep-small-title mu-seconday-color"><?php echo $podtytul; ?></p>
                                    <?php endif; ?>
                                    <?php if($tytul): ?>
                                    <!-- <p class="mu-heading mu-heading-pre">03/</p> -->
                                        <h3 class="mu-heading mu-heading-brown"><?php echo $tytul; ?></h3>
                                    <?php endif; ?>
                                    <div class="row">
                                        <div class="col-lg-4">
                                            <div class="mu-buildings-slider-content--features">
                                            <?php if( have_rows('cecha') ): 
                                                while( have_rows('cecha') ): the_row();
                                                    $ikona = get_sub_field('ikona'); 
                                                    $nazwa = get_sub_field('nazwa'); 
                                                    $wartosc = get_sub_field('wartosc'); ?>
                                                    <p class="mu-buildings-slider-content-features--item">
                                                    <?php if($ikona): ?><img alt="" src="<?php echo $ikona; ?>"><?php endif;?>
                                                    <?php if($nazwa): ?><?php echo $nazwa; ?><?php endif; ?>
                                                    <?php if($wartosc): ?><span><?php echo $wartosc; ?></span><?php endif; ?>
                                                    </p>
                                                <?php endwhile;
                                            endif; ?>
                                            </div>
                                            <?php if($przycisk): ?>
                                                <a class="mu-primary-btn mu-primary-btn-color-main" target="<?php echo $przycisk['target']; ?>" href="<?php echo $przycisk['url']; ?>"><?php echo $przycisk['title']; ?> <i class="fas fa-long-arrow-alt-right"></i></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if($zdjecie): ?>
                            <img alt="" class="mu-buildings-slider-container--slide-image" src="<?php echo $zdjecie; ?>">
                        <?php endif; ?>
                    </div>
                    <?php endwhile;
                endif; ?>
                </div>
            </div>
        <?php endwhile;
    endif; ?>
    <div class="container mu-features-container">
        <div class="row">
            <div class="col-lg-12">
                <?php if( have_rows('sekcja_-_cechy_naszych_nieruchomosci') ): 
                    while( have_rows('sekcja_-_cechy_naszych_nieruchomosci') ): the_row(); 
                        $tytul = get_sub_field('tytul'); ?>
                        <?php if($tytul): ?>
                            <h2 class="mu-heading"><?php echo $tytul; ?></h2>
                        <?php endif; ?>
                        <div class="mu-features-boxes">
                        <?php if( have_rows('box') ): 
                            while( have_rows('box') ): the_row(); 
                                $ikona = get_sub_field('ikona');
                                $podtytul = get_sub_field('podtytul');
                                $tytul = get_sub_field('tytul');
                                $tekst = get_sub_field('tekst');
                            ?>
                                <div class="mu-features-boxes--item">
                                    <?php if($ikona): ?>
                                        <img alt="" src="<?php echo $ikona; ?>">
                                    <?php endif; ?>
                                    <?php if($podtytul): ?>
                                        <p class="mu-features-boxes-item--sub-title"><?php echo $podtytul; ?></p>
                                    <?php endif; ?>
                                    <?php if($tytul): ?>
                                        <h3 class="mu-features-boxes-item--title"><?php echo $tytul; ?></h3>
                                    <?php endif; ?>
                                    <?php if($tekst): ?>
                                        <p class="mu-features-boxes-item--desc"><?php echo $tekst; ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endwhile;
                            endif; ?>
                        </div>
                        <?php endwhile;
                    endif; ?>
            </div>
        </div>
    </div>
    <div class="mu-homepage-contact-content text-white">
        <div class="container">
            <?php if( have_rows('sekcja_-_kontakt') ): 
                    while( have_rows('sekcja_-_kontakt') ): the_row(); 
                        $podtytul = get_sub_field('podtytul'); 
                        $tytul = get_sub_field('tytul'); 
                        $tekst = get_sub_field('tekst'); 
                        $zdjecie = get_sub_field('zdjecie'); 
                        $imie_i_nazwisko = get_sub_field('imie_i_nazwisko'); 
                        $stanowisko = get_sub_field('stanowisko'); 
                        $numer_telefonu = get_sub_field('numer_telefonu'); 
                        $adres_email = get_sub_field('adres_e-mail'); 
                        ?>
                        <?php if($podtytul): ?>
                            <p class="mu-sep-small-title mu-white-color"><?php echo $podtytul; ?></p>
                        <?php endif; ?>
                        <?php if($tytul): ?>
                            <h2 class="mu-heading mu-secondary-color"><?php echo $tytul; ?></h2>
                        <?php endif; ?>
                        <div class="mu-contact-content-container">
                            <div class="mu-contact-content-container--column">
                                <?php if($tekst): ?>
                                    <div class="mu-light-content">
                                        <?php echo $tekst; ?>
                                    </div>
                                <?php endif; ?>
                                <div class="mu-contact-person--item">
                                    <?php if($zdjecie): ?>
                                        <img alt="" src="<?php echo $zdjecie; ?>">
                                    <?php endif; ?>
                                    <div class="mu-contact-person-item--content">
                                        <?php if($imie_i_nazwisko): ?>
                                            <p class="mu-contact-person-item-content--name"><?php echo $imie_i_nazwisko; ?></p>
                                        <?php endif; ?>
                                        <?php if($stanowisko): ?>
                                            <p class="mu-contact-person-item-content--role"><?php echo $stanowisko; ?></p>
                                        <?php endif; ?>
                                        <?php if($numer_telefonu): ?>
                                            <p class="mu-contact-person-item-content--phone mu-contact-person-item-content--phone-white"><?php echo $numer_telefonu; ?></p>
                                        <?php endif; ?>
                                        <?php if($adres_email): ?>
                                            <p class="mu-contact-person-item-content--mail mu-contact-person-item-content--mail-white"><?php echo $adres_email; ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="mu-contact-content-container--column mu-light-content">
                                <?php echo do_shortcode('[contact-form-7 id="77" title="Formularz kontaktowy strona główna"]'); ?>
                            </div>
                        </div>
                <?php endwhile;
            endif; ?>
        </div>
    </div>
    <?php if( have_rows('sekcja_-_nasza_oferta') ):  ?>
        <div class="container mu-our-offer">
            <div class="row">
            <?php while( have_rows('sekcja_-_nasza_oferta') ): the_row(); 
                    $podtytul = get_sub_field('podtytul'); 
                    $tytul = get_sub_field('tytul'); 
                    $tekst = get_sub_field('tekst'); 
                    $przycisk = get_sub_field('przycisk'); ?>
                    <div class="col-lg-5 mu-col-vertical-center">
                        <?php if($podtytul): ?>
                            <p class="mu-sep-small-title mu-seconday-color">Nasza oferta</p>
                        <?php endif; ?>
                        <?php if($tytul): ?>
                            <h2 class="mu-heading"><?php echo $tytul; ?></h2>
                        <?php endif; ?>
                        <?php if($tekst): ?>
                            <div class="mu-light-content">
                                <?php echo $tekst; ?>
                            </div>
                        <?php endif; ?>
                        <?php if($przycisk): ?>
                            <a target="<?php echo $przycisk['target'];?>" class="mu-primary-btn mu-primary-btn-color-main" href="<?php echo $przycisk['url'];?>"><?php echo $przycisk['title'];?> <i class="fas fa-long-arrow-alt-right"></i></a>
                        <?php endif; ?>
                    </div>
                    <div class="offset-lg-1 col-lg-6">
                        <div class="mu-our-offer-carousel">
                            <?php if( have_rows('slider') ): 
                                while( have_rows('slider') ): the_row(); 
                                    $grafika = get_sub_field('grafika'); ?>
                                        <img alt="" src="<?php echo $grafika; ?>">
                                <?php endwhile;
                            endif; ?>
                        </div>
                    </div>
            <?php endwhile; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if( have_rows('sekcja_-_zdjecia') ): 
            while( have_rows('sekcja_-_zdjecia') ): the_row(); ?>
                <div class="mu-photos">
                <?php if( have_rows('zdjecie') ): 
                    while( have_rows('zdjecie') ): the_row();
                    $grafika = get_sub_field('grafika'); ?>
                        <img alt="" src="<?php echo $grafika; ?>">
                    <?php endwhile;
                endif; ?>
                </div>
        <?php endwhile;
    endif; ?>
    <?php if( have_rows('sekcja_-_cta') ): 
            while( have_rows('sekcja_-_cta') ): the_row();
            $tekst = get_sub_field('tekst'); 
            $przycisk = get_sub_field('przycisk'); ?>
            <div class="mu-cta">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-6">
                            <?php if($tekst): ?>
                                <span class="mu-cta-slogan"><?php echo $tekst; ?></span>
                            <?php endif;?>
                        </div>      
                        <div class="col-sm-6">
                            <?php if($przycisk): ?>
                                <a target="<?php echo $przycisk['target'];?>" class="mu-primary-btn mu-primary-btn-color-2" href="<?php echo $przycisk['url'];?>"><?php echo $przycisk['title'];?> <i class="fas fa-long-arrow-alt-right"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endwhile;
    endif; ?>
<script>
	(function($) {
		$( document ).ready(function() {
			$('.mu-page-header-carousel').slick({
				infinite: false,
				dots: false,
				arrows: true,
				slidesToShow: 1,
				slidesToScroll: 1
			});
			$('.mu-our-offer-carousel').slick({
				infinite: true,
				dots: true,
				arrows: false,
				slidesToShow: 1,
				slidesToScroll: 1
			});
			$('.mu-buildings-slider-carousel').slick({
				infinite: true,
				dots: false,
				arrows: true,
				slidesToShow: 1,
				slidesToScroll: 1
			});
		});
	})(jQuery);
</script>
<?php
get_footer();
