/*
Theme Name: Muchowiecka
Version: 1.0.0
Text Domain: muchowiecka
*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Normalize
# Typography
# Elements
# Forms
# Navigation
	## Links
	## Menus
# Accessibility
# Alignments
# Clearings
# Widgets
# Content
	## Posts and pages
	## Comments
# Infinite scroll
# Media
	## Captions
	## Galleries
# Woocommerce
# Footer
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Normalize
--------------------------------------------------------------*/
body {
	margin: 0;
	-webkit-font-smoothing: auto;
	font-family: 'Roboto Condensed', sans-serif;
}

.sr-only {
	border: 0 !important;
	clip: rect(1px, 1px, 1px, 1px) !important;
	-webkit-clip-path: inset(50%) !important;
	clip-path: inset(50%) !important;
	height: 1px !important;
	overflow: hidden !important;
	margin: -1px !important;
	padding: 0 !important;
	position: absolute !important;
	width: 1px !important;
	white-space: nowrap !important;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
	display: block;
}

audio,
canvas,
progress,
video {
	display: inline-block;
	vertical-align: baseline;
}

audio:not([controls]) {
	display: none;
	height: 0;
}

[hidden],
template {
	display: none;
}

a {
	background-color: transparent;
}

a:active,
a:hover {
	outline: 0;
}

dfn {
	font-style: italic;
}

mark {
	background: #ff0;
	color: #000;
}

small {
	font-size: 80%;
}

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

img {
	border: 0;
}

svg:not(:root) {
	overflow: hidden;
}

figure {
	margin: 1em 2.5rem;
}

hr {
	box-sizing: content-box;
	height: 0;
}

button {
	overflow: visible;
}

button,
select {
	text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
	-webkit-appearance: button;
	cursor: pointer;
}

button[disabled],
html input[disabled] {
	cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

input {
	line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

fieldset {
	border: 1px solid #c0c0c0;
	margin: 0 2px;
	padding: 0.35em 0.625em 0.75em;
}

legend {
	border: 0;
	padding: 0;
}

textarea {
	overflow: auto;
}

optgroup {
	font-weight: bold;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

td,
th {
	padding: 0;
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
h1, h2, h3, h4, h5, h6 {
	clear: both;
	margin-top: 24px;
	margin-bottom: 15px;
}
h1 {
	font-size: 32px;
	padding-bottom: 10px;
}
h2 {
	font-size: 24px;
	padding-bottom: 0.3em;
	line-height: 1.25;
}
h3 {
	font-size: 18px;
	line-height: 25px;
}
h4 {
	font-size: 16px;
	line-height: 20px;
}
h5 {
	font-size: 14px;
	line-height: 17.5px;
}

p {
	margin-bottom: 1.5em;
}
h1.entry-title {
	font-size: 1.31rem;
	border-bottom: 1px solid #eaecef;
}
h2.entry-title {
	border-bottom: 1px solid #eaecef;
}
h2.widget-title {
	font-size: 1.2rem;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
body {
	background: #fff;
	/* Fallback for when there is no custom background color defined. */
}

img {
	height: auto;
	/* Make sure images are scaled correctly. */
	max-width: 100%;
	/* Adhere to container width. */
}

figure {
	margin: 1em 0;
	/* Extra wide images within figure tags don't overflow the content area. */
}

table {
	margin: 0 0 1.5em;
	width: 100%;
}

/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
header#masthead {
	margin-bottom: 0;
	padding: .74rem 1rem 0;
	position: absolute;
	width: 100%;
	border-top: 3rem solid #191919;
	box-shadow: none;
	z-index: 9;
}

#masthead #main-nav {
	height: 100%;
	background: #191919;
}

#masthead .navbar {
	align-items: stretch;
}

.navbar-brand > a {
	color: rgba(0, 0, 0, 0.9);
	font-size: 1.1rem;
	outline: medium none;
	text-decoration: none;
	color: #fff;
	font-weight: 700;
}

.navbar-brand > a:visited, .navbar-brand > a:hover {
	text-decoration: none;
}

#page-sub-header {
	position: relative;
	padding-top: 5rem;
	padding-bottom: 0;
	text-align: center;
	font-size: 1.25rem;
	background-size: cover !important;
}

body:not(.theme-preset-active) #page-sub-header h1 {
	line-height: 1.6;
	font-size: 4rem;
	color: #563e7c;
	margin: 0 0 1rem;
	border: 0;
	padding: 0;
}

#page-sub-header p {
	margin-bottom: 0;
	line-height: 1.4;
	font-size: 1.25rem;
	font-weight: 300;
}
body:not(.theme-preset-active) #page-sub-header p {
color: #212529;
}
a.page-scroller {
	color: #333;
	font-size: 2.6rem;
	display: inline-block;
	margin-top: 2rem;
}

@media screen and (min-width: 768px) {
	body:not(.theme-preset-active) #page-sub-header h1 {
		font-size: 3.750rem;
	}
	body:not(.theme-preset-active) #page-sub-header {
		font-size: 1.25rem;
	}
}
@media screen and (min-width: 992px) {
	#page-sub-header p {
		max-width: 43rem;
		margin: 0 auto;
	}
}
/*--------------------------------------------------------------
## Links
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Menus
--------------------------------------------------------------*/
#masthead nav {
	padding-left: 0;
	padding-right: 0;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li > a {
	color: #cdbfe3;
	padding: 0.5rem;
	font-weight: 500;
	font-size: 0.875rem;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li > a:hover,
body:not(.theme-preset-active) #masthead .navbar-nav > li.current_page_item > a {
	color: #fff;
	font-weight: 600;
	/*background: #f9f9f9;*/
}

.navbar-brand {
	height: auto;
}

.navbar-toggle .icon-bar {
	background: #000 none repeat scroll 0 0;
}

.dropdown-menu .dropdown-toggle::after {
	border-bottom: 0.3em solid transparent;
	border-left: 0.3em solid;
	border-top: 0.3em solid transparent;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children {
		position: relative;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children>.dropdown-menu {
		top: 0;
		left: 100%;
		margin-top: -6px;
		margin-left: -1px;
		-webkit-border-radius: 0 6px 6px 6px;
		-moz-border-radius: 0 6px 6px;
		border-radius: 0 6px 6px 6px;
}
.dropdown.menu-item-has-children .dropdown.menu-item-has-children:hover>.dropdown-menu {
		display: block;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children>a:after {
		display: block;
		content: " ";
		float: right;
		width: 0;
		height: 0;
		border-color: transparent;
		border-style: solid;
		border-width: 5px 0 5px 5px;
		border-left-color: #ccc;
		margin-top: 5px;
		margin-right: -10px;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children:hover>a:after {
		border-left-color: #fff;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children.pull-left {
		float: none;
}

.dropdown.menu-item-has-children .dropdown.menu-item-has-children.pull-left>.dropdown-menu {
		left: -100%;
		margin-left: 10px;
		-webkit-border-radius: 6px 0 6px 6px;
		-moz-border-radius: 6px 0 6px 6px;
		border-radius: 6px 0 6px 6px;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
	display: block;
}

.dropdown-item {
	line-height: 1.2;
	padding-bottom: 0.313rem;
	padding-top: 0.313rem;
}

.dropdown-menu {
	min-width: 12.500rem;
}

.dropdown .open .dropdown-menu {
	display: block;
	left: 12.250em;
	top: 0;
}

.dropdown-menu .dropdown-item {
	white-space: normal;
	background: transparent;
	line-height: 1.6;
}
.dropdown-menu .dropdown-item:hover {
	background: transparent;
}

@media screen and (min-width: 37.5em) {
	.menu-toggle {
		display: none;
	}
}
@media screen and (min-width: 769px) {
	.dropdown-menu li > .dropdown-menu {
		right: -9.875rem;
		top: 1.375rem;
	}
}
@media screen and (max-width: 991px) {
	.navbar-nav .dropdown-menu {
		border: medium none;
		margin-left: 1.250rem;
		padding: 0;
	}

	.dropdown-menu li a {
		padding: 0;
	}

	#masthead .navbar-nav > li > a {
		padding-bottom: 0.625rem;
		padding-top: 0.313rem;
	}

	.navbar-light .navbar-toggler {
		border: medium none;
		outline: none;
	}
}
.site-main .comment-navigation,
.site-main .posts-navigation,
.site-main .post-navigation {
	margin: 0 0 1.5em;
	overflow: hidden;
}

.comment-navigation .nav-previous,
.posts-navigation .nav-previous,
.post-navigation .nav-previous {
	float: left;
	width: 50%;
}

.comment-navigation .nav-next,
.posts-navigation .nav-next,
.post-navigation .nav-next {
	float: right;
	text-align: right;
	width: 50%;
}
.comment-content.card-block {
	padding: 20px;
}

.navigation.post-navigation {
	padding-top: 1.875rem;
}

.post-navigation .nav-previous a,
.post-navigation .nav-next a {
	border: 1px solid #ddd;
	border-radius: 0.938rem;
	display: inline-block;
	padding: 0.313rem 0.875rem;
	text-decoration: none;
}

.post-navigation .nav-next a::after {
	content: " \2192";
}

.post-navigation .nav-previous a::before {
	content: "\2190 ";
}

.post-navigation .nav-previous a:hover,
.post-navigation .nav-next a:hover {
	background: #eee none repeat scroll 0 0;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
	clip: rect(1px, 1px, 1px, 1px);
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	word-wrap: normal !important;
	/* Many screen reader and browser combinations announce broken words as they would appear visually. */
}
.screen-reader-text:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	color: #21759b;
	display: block;
	font-size: 14px;
	font-size: 0.875rem;
	font-weight: bold;
	height: auto;
	left: 0.313rem;
	line-height: normal;
	padding: 0.938rem 1.438rem 0.875rem;
	text-decoration: none;
	top: 0.313rem;
	width: auto;
	z-index: 100000;
	/* Above WP toolbar. */
}

/* Do not show the outline on the skip link target. */
#content[tabindex="-1"]:focus {
	outline: 0;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
	display: inline;
	float: left;
	margin-right: 1.5em;
}

.alignright {
	display: inline;
	float: right;
	margin-left: 1.5em;
}

.aligncenter {
	clear: both;
	display: block;
	margin-left: auto;
	margin-right: auto;
}

a img.alignright {
	float: right;
	margin: 0.313rem 0 1.25rem 1.25rem;
}

a img.alignnone {
	margin: 0.313rem 1.25rem 1.25rem 0;
}

a img.alignleft {
	float: left;
	margin: 0.313rem 1.25rem 1.25rem 0;
}

a img.aligncenter {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption.alignnone {
	margin: 0.313rem 1.25rem 1.25rem 0;
}

.wp-caption.alignleft {
	margin: 0.313rem 1.25rem 1.25rem 0;
}

.wp-caption.alignright {
	margin: 0.313rem 0 1.25rem 1.25rem;
}

/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
	content: "";
	display: table;
	table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
	clear: both;
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widget {
	margin: 0 0 1.5em;
	font-size: 0.875rem;
	/* Make sure select elements fit in widgets. */
}
.widget select {
	max-width: 100%;
}

.widget_search .search-form input[type="submit"] {
	display: none;
}

.nav > li > a:focus,
.nav > li > a:hover {
	background-color: #eee;
	text-decoration: none;
}
.half-rule {
	width: 6rem;
	margin: 2.5rem 0;
}
.widget_categories .nav-link {
	display: inline-block;
}

/*--------------------------------------------------------------
# Content
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
#content.site-content {
	padding-bottom: 3.75rem;
	padding-top: 4.125rem;
}

.sticky .entry-title::before {
	content: '\f08d';
	font-family: "Font Awesome\ 5 Free";
	font-size: 1.563rem;
	left: -2.5rem;
	position: absolute;
	top: 0.375rem;
	font-weight: 900;
}

.sticky .entry-title {
	position: relative;
}

.single .byline,
.group-blog .byline {
	display: inline;
}

.page-content,
.entry-content,
.entry-summary {
	margin: 1.5em 0 0;
}

.page-links {
	clear: both;
	margin: 0 0 1.5em;
}

.page-template-blank-page .entry-content,
.blank-page-with-container .entry-content {
	margin-top: 0;
}

.post.hentry {
	margin-bottom: 4rem;
}

.posted-on, .byline, .comments-link {
	color: #9a9a9a;
}

.entry-title > a {
	color: inherit;
}

/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
.comment-content a {
	word-wrap: break-word;
}

.bypostauthor {
	display: block;
}

.comment-body .pull-left {
	padding-right: 0.625rem;
}

.comment-list .comment {
	display: block;
}

.comment-list {
	padding-left: 0;
}

.comments-title {
	font-size: 1.125rem;
}

.comment-list .pingback {
	border-top: 1px solid rgba(0, 0, 0, 0.125);
	padding: 0.563rem 0;
}

.comment-list .pingback:first-child {
	border: medium none;
}

/*--------------------------------------------------------------
# Infinite scroll
--------------------------------------------------------------*/
/* Globally hidden elements when Infinite Scroll is supported and in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
	/* Theme Footer (when set to scrolling) */
	display: none;
}

/* When Infinite Scroll has reached its end we need to re-display elements that were hidden (via .neverending) before. */
.infinity-end.neverending .site-footer {
	display: block;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	border: none;
	margin-bottom: 0;
	margin-top: 0;
	padding: 0;
}

/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
	max-width: 100%;
}

/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
	background: #f1f1f1 none repeat scroll 0 0;
	border: 1px solid #f0f0f0;
	max-width: 96%;
	padding: 0.313rem 0.313rem 0;
	text-align: center;
}
.wp-caption img[class*="wp-image-"] {
	border: 0 none;
	height: auto;
	margin: 0;
	max-width: 100%;
	padding: 0;
	width: auto;
}
.wp-caption .wp-caption-text {
	font-size: 0.688rem;
	line-height: 1.063rem;
	margin: 0;
	padding: 0.625rem;
}

.wp-caption-text {
	text-align: center;
}

/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.gallery {
	margin-bottom: 1.5em;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	vertical-align: top;
	width: 100%;
}
.gallery-item .gallery-columns-2 {
	max-width: 50%;
}
.gallery-item .gallery-columns-3 {
	max-width: 33.33333%;
}
.gallery-item .gallery-columns-4 {
	max-width: 25%;
}
.gallery-item .gallery-columns-5 {
	max-width: 20%;
}
.gallery-item .gallery-columns-6 {
	max-width: 16.66667%;
}
.gallery-item .gallery-columns-7 {
	max-width: 14.28571%;
}
.gallery-item .gallery-columns-8 {
	max-width: 12.5%;
}
.gallery-item .gallery-columns-9 {
	max-width: 11.11111%;
}

.gallery-caption {
	display: block;
}

/*--------------------------------------------------------------
# Plugin Compatibility
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Woocommerce
--------------------------------------------------------------*/
.woocommerce-cart-form .shop_table .coupon .input-text {
	width: 8.313rem !important;
}

.variations_form .variations .value > select {
	margin-bottom: 0.625rem;
}

.woocommerce-MyAccount-content .col-1,
.woocommerce-MyAccount-content .col-2 {
	max-width: 100%;
}

/*--------------------------------------------------------------
## Elementor
--------------------------------------------------------------*/
.elementor-page article .entry-footer {
	display: none;
}

.elementor-page.page-template-fullwidth #content.site-content {
	padding-bottom: 0;
	padding-top: 0;
}

.elementor-page .entry-content {
	margin-top: 0;
}

/*--------------------------------------------------------------
## Visual Composer
--------------------------------------------------------------*/
.vc_desktop article .entry-footer {
	display: none;
}

.vc_desktop #content.site-content {
	padding-bottom: 0;
	padding-top: 0;
}

.vc_desktop .entry-content {
	margin-top: 0;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
footer#colophon {
	font-size: 85%;
}
body:not(.theme-preset-active) footer#colophon {
	color: #99979c;
	background-color: #f7f7f7;
}
.navbar-dark .site-info {
	color: #fff;
}
.copyright {
	font-size: 0.875rem;
	margin-bottom: 0;
	text-align: center;
}

.copyright a, footer#colophon a {
	color: inherit;
}

@media screen and (max-width: 767px) {
	#masthead .navbar-nav > li > a {
		padding-bottom: 0.938rem;
		padding-top: 0.938rem;
	}
}
/*--------------------------------------------------------------
# Media Query
--------------------------------------------------------------*/
/*--------------------------------------------------------------
## Notebook
--------------------------------------------------------------*/
@media only screen and (max-width: 1280px) {
	html {
		font-size: 95%;
	}
}

@media screen and (max-width: 1199px) {
	.navbar-dark .dropdown-item {
		color: #fff;
	}
	.navbar-nav .dropdown-menu {
		background: transparent;
		box-shadow: none;
		border: none;
	}
}
/*--------------------------------------------------------------
## Netbook
--------------------------------------------------------------*/
@media only screen and (max-width: 1024px) {
	html {
		font-size: 90%;
	}
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 960px) {
	html {
		font-size: 85%;
	}
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 768px) {
	html {
		font-size: 80%;
	}
}
/*--------------------------------------------------------------
## iPad
--------------------------------------------------------------*/
@media only screen and (max-width: 480px) {
	html {
		font-size: 75%;
	}
}

a.mu-floating-make-appointment {
	background: #C49A6C;
	color: #fff;
	text-decoration: none;
	padding: 1rem .8rem;
	text-transform: uppercase;
	writing-mode: vertical-rl;
	text-orientation: mixed;
	position: fixed;
	top: 10rem;
	right: 0;
	transition: .2s linear;
	font-family: 'Oswald', sans-serif;
	line-height: 1;
}

a.mu-floating-make-appointment:hover {
	transition: .2s linear;
	background: #ab855d;
}

#masthead nav {
	margin-top: -2.5rem;
}

.mu-page-header {
	display: flex;
	background-size: cover;
	height: 44rem;
	flex-flow: column;
	justify-content: center;
	font-family: 'Abhaya Libre', serif;
	position: relative;
	z-index: 0;
}

p.mu-page-header--pagename {
	color: #fff;
	opacity: .3;
	font-size: 4rem;
	margin-bottom: 0;
	line-height: 1;
	padding: 0 8rem;
}

.mu-page-header h1 {
	font-size: 3rem;
	color: #fff;
	padding: 0 8rem;
}

.mu-about-content {
	padding-top: 7rem;
	padding-bottom: 5rem;
}

p.mu-sep-small-title {
	font-family: 'Oswald', sans-serif;
}

.mu-seconday-color {
	color: #C49A6C;
}

.mu-white-color {
	color: #fff;
}

p.mu-sep-small-title:before {
	content: ' ';
	width: 3.5rem;
	display: inline-block;
	height: 3px;
	margin-right: 1rem;
	background: #333;
}

p.mu-sep-small-title.mu-seconday-color:before {
	background: #C49A6C;
}

p.mu-sep-small-title.mu-white-color:before {
	background: #fff;
}

p.mu-heading, h1.mu-heading, h2.mu-heading, h3.mu-heading {
	font-size: 2.8rem;
	line-height: 1;
	font-family: 'Abhaya Libre', serif;
	margin-bottom: 2rem;
	font-weight: 400;
}

.mu-heading-brown {
	color: #3c2415;
}

.mu-secondary-color {
	color: #C49A6C!important;
}

.mu-light-content {
	font-weight: 300;
}

.mu-about-bg {
	background: #F1F1F1;
	padding: 3.5rem 3rem;
}

.mu-about-bg--heading {
	color: #C49A6C;
	font-weight: 600;
}

.mu-light-content strong {
	font-weight: 400;
}

a.mu-primary-btn {
	background: #191919;
	transition: .2s linear;
	color: #fff;
	text-transform: uppercase;
	padding: 1rem;
	display: table;
	margin-top: 3rem;
	text-decoration: none;
}

a.mu-primary-btn:hover {
	transition: .2s linear;
	opacity: .8;
}

.mu-btns-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

a.mu-primary-btn.mu-primary-btn-color-1 {
	background: #191919;
}

a.mu-primary-btn.mu-primary-btn-color-2 {
	background: #6D6E71;
}

a.mu-primary-btn.mu-primary-btn-color-3 {
	background: #B4B4B4;
}

a.mu-primary-btn.mu-primary-btn-color-main {
	background: #C49A6C;
}

a.mu-primary-btn i {
	margin-left: 1rem;
}

.mu-abount-content-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.mu-abount-content-container--column {
	width: 45%;
}

.mu-about-contact-data {
	color: #fff;
	padding: 5rem;
	background-size: cover;
}

.mu-about-contact-data h2 {
	text-align: center;
	font-size: 2.7rem;
	color: #fff;
	font-family: 'Abhaya Libre', serif;
	font-weight: 400;
	margin-bottom: 5rem;
}

.mu-about-contact-data--container {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
}

.mu-about-contact-data-container--column {
	width: 28%;
}

.mu-about-partners {
	padding-top: 7rem;
	padding-bottom: 5rem;
}

.mu-about-contact-data--container h2 {
	width: 100%;
}

.mu-about-contact-data--container {
	border: 1px solid #fff;
	padding-top: 2rem;
	padding-bottom: 3rem;
}


.mu-about-contacts-row {
	margin-bottom: 15px;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.mu-about-contacts-text {
}
.mu-about-contacts-photo {
	margin-right: 15px;
}

.list-docs {
	list-style: none;
	margin: 0;
	padding: 0 0 0 10px;
}
.list-docs li {
	margin-bottom: 5px;
}
.list-docs li a,
.list-docs li a:hover {
	color: #191919;
	font-weight: 400;
	text-decoration: none;
}
.list-docs li a i {
	color: #c49a6c;
	margin-right: 5px;
	font-size: 22px;
}
body:not(.theme-preset-active) #masthead .navbar-nav > li > a {
	color: #fff;
	font-size: 1rem;
	text-transform: uppercase;
	margin: 0 .6rem;
	transition: .2s linear;
	border-bottom: 1px solid transparent;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li > a:hover {
	font-weight: 400;
	transition: .2s linear;
	opacity: .6;
}

div#footer-widget {
	background-color: #1B1918!important;
	color: #fff;
	padding: 6rem 0;
}

#footer-widget h2.widget-title {
	color: #fff;
	font-size: 1rem;
	text-transform: uppercase;
	font-family: 'Oswald', sans-serif;
	font-weight: 400;
	margin-bottom: 2.5rem;
}

#footer-widget a.nav-link {
	padding: .5rem 0;
	color: #7C7B7B;
	border-bottom: 1px solid #7C7B7B;
	transition: .2s linear;
}

#footer-widget a.nav-link:hover {
	transition: .2s linear;
	background: #1B1918;
	padding: .5rem .5rem;
}

body:not(.theme-preset-active) footer#colophon {
	background: #121110;
}

div#footer-widget .widget_media_image {
	margin-bottom: 3rem;
}

div#footer-widget .widget_media_image img {
	width: 8rem;
}

.navbar-brand:after {
	content: ' ';
	height: .2rem;
	width: 2rem;
	background: #C49A6C;
	display: inline-block;
	float: right;
	margin-left: 1.5rem;
	margin-top: 5rem;
}

.navbar-logo:focus img {
	border: 2px solid white;
}

.mu-contact-content-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.mu-contact-content-container--column {
	width: 46.5%;
}

.mu-dark-content {
	background: #191919;
	padding: 4rem 3rem;
}

.mu-contact-content-container--column input, .mu-contact-content-container--column textarea, .mu-contact-content-container--column select {
	background: transparent!important;
	color: #fff!important;
	border-radius: 0;
	font-weight: 600;
	font-family: 'Roboto Condensed', sans-serif;
	font-size: .9rem;
	padding: 0 2rem;
}

.mu-contact-content-container--column textarea {
	padding: .5rem 2rem;
	height: 14rem;
}

.mu-contact-content-container--column input::placeholder, .mu-contact-content-container--column textarea::placeholder {
	color: #fff;
}

.mu-contact-content-container--column  span.wpcf7-list-item-label {
	color: #fff;
	font-size: .8rem;
	margin-left: 2rem;
	display: block;
}

.mu-contact-content-container--column span.wpcf7-list-item {
	margin: 0;
}

.mu-contact-content-container--column  input[type="checkbox"] {
	float: left;
	margin-top: .2rem;
}

div.wpcf7 .wpcf7-submit {
	background: #C49A6C!important;
	color: #fff!important;
	opacity: 1!important;
	border-radius: 0;
	text-transform: uppercase;
	border: none;
	font-family: 'Oswald', sans-serif;
	font-size: 1rem;
	float: right;
	padding: .75rem 4rem;
	margin-top: 2rem;
	transition: .2s linear;
}

div.wpcf7 .wpcf7-submit:hover {
	transition: .2s linear;
	opacity: .7!important;
}

.mu-offer-item {
	padding: 4.5rem 0;
}

.mu-offer-item:nth-child(even) {
	background: #F1F1F1;
}

.mu-offer-item-content--title {
	font-family: 'Abhaya Libre', serif;
	font-size: 2.2rem;
	margin-bottom: 1.5rem;
	color: #231F20;
}

p.mu-offer-item-content--categories {
	font-size: .9rem;
	color: #C49A6C;
	font-family: 'Oswald', sans-serif;
}

p.mu-offer-item-content--categories:before {
	content: ' ';
	display: inline-block;
	width: 3rem;
	height: 2px;
	background: #C49A6C;
	margin-right: 1rem;
	float: left;
	margin-top: .8rem;
}

.mu-offer-item-content--desc {
	font-family: 'Roboto Condensed', sans-serif;
	font-weight: 300;
	margin-bottom: 3rem;
}

.mu-offer-item-content--params {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.mu-offer-item-content--params span {
	flex: 1 1 50%;
	margin-bottom: 1.3rem;
}

span.mu-offer-item-content-params--size {
	font-weight: 600;
}

span.mu-offer-item-content-params--price {
	font-weight: 600;
}

.mu-offer-item--content {
	padding: 0 4rem;
}

.mu-our-buildings-item {
	display: flex;
	flex-wrap: wrap;
}

.mu-our-buildings-item img {
	width: 50%;
}

.mu-our-buildings-item--content {
	width: 50%;
	display: flex;
	flex-flow: column;
	flex-wrap: wrap;
	justify-content: center;
	align-items: baseline;
}

.mu-our-buildings-item-content--title {
	font-size: 2.6rem;
	font-family: 'Abhaya Libre', serif;
	margin-bottom: 0;
	line-height: 1;
}

.mu-our-buildings-item--content:nth-child(even) {
	padding-left: 8rem;
	padding-right: 15rem;
}

.mu-our-buildings-item--content:nth-child(odd) {
	padding-left: 15rem;
	padding-right: 8rem;
}

.mu-our-buildings-item--content:nth-child(even) a {
	background: #191919;
}

article.post .entry-content p:nth-child(1) {
	font-size: 1.4rem;
	font-family: 'Roboto Condensed', sans-serif;
	margin-bottom: 3rem;
	margin-top: 2rem;
	font-weight: 400;
}

article.post .entry-content{
	font-family: 'Roboto Condensed', sans-serif;
	font-weight: 300;
}

.mu-single-meta {
	color: #C49A6C;
	font-family: 'Oswald', sans-serif;
	font-size: .9rem;
	margin-top: 3rem;
}

.mu-content {
	font-weight: 300;
}

.mu-team-content {
	padding-top: 3rem;
	padding-bottom: 3rem;
}

.mu-team-items {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.mu-team--item {
	width: 50%;
	display: flex;
	margin-bottom: 6rem;
}

.mu-team--item img {
	max-width: 40%;
	width: 40%;
}

.mu-team-item--content {
		display: flex;
		flex-wrap: wrap;
		flex-flow: column;
		justify-content: center;
		padding: 0 3rem;
		line-height: 1.1;
}

.mu-team-item--content p {
		width: 100%;
}

p.mu-team-item-content--name {
		font-family: 'Abhaya Libre', serif;
		font-size: 2rem;
		margin-bottom: .5rem;
}

p.mu-team-item-content--role {
		font-family: 'Roboto Condensed', sans-serif;
		text-transform: uppercase;
		font-weight: 300;
}

p.mu-team-item-content--phone {
		margin-bottom: .5rem;
		font-weight: 300;
}

p.mu-team-item-content--mail {
		margin-bottom: 0;
		font-weight: 300;
}

p.mu-team-item-content--phone:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-phone.png');
	width: 1rem;
	height: 1.1rem;
	content: ' ';
	display: inline-block;
	margin-right: 1rem;
	float: left;
}

p.mu-team-item-content--mail:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-mail.png');
	width: 1rem;
	height: .85rem;
	content: ' ';
	display: inline-block;
	margin-right: 1rem;
	float: left;
	margin-top: .3rem;
}

body:not(.theme-preset-active) #masthead .navbar-nav > li.current_page_item > a:hover {
	font-weight: 600;
}

.mu-news-categories {
	padding-top: 3rem;
	padding-bottom: 3rem;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}

.mu-news-categories ul {
	list-style: none;
	padding-left: 0;
	margin-left: 0;
	display: flex;
	justify-content: space-between;
}

.mu-news-categories ul a {
	padding: 1.5rem 2rem;
	color: #B4B4B4;
	font-family: 'Oswald', sans-serif;
	font-weight: 400;
	text-transform: uppercase;
	transition: .2s linear;
	border-bottom: 2px solid #B4B4B4;
	margin: 0 .8rem;
}

.mu-news-categories ul a:hover {
	transition: .2s linear;
	text-decoration: none;
	color: #C49A6C;
	border-bottom: 2px solid #C49A6C;
}

.mu-news-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.mu-news-item {
	flex: 0 0 25%;
	margin: 2rem;
	-webkit-box-shadow: 0px 0px 9px 0px rgba(216px, 216px, 216px, .75);
	-moz-box-shadow: 0px 0px 9px 0px rgba(216,216,216,0.75);
	box-shadow: 0px 0px 9px 0px rgb(216 216 216 / 75%);
}

.mu-news-item--thumbnail {
	height: 14rem;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

.mu-news-item--content {
	padding: 2rem;
	text-align: center;
}

p.mu-news-item-content--category-meta {
	color: #C49A6C;
	font-family: 'Oswald', sans-serif;
	font-size: .9rem;
}

.mu-news-item-content--title {
	font-size: 1.3rem;
	font-family: 'Abhaya Libre', serif;
	margin-bottom: 1rem;
}

.mu-news-item-content--desc {
	font-weight: 300;
	margin-bottom: 0;
}

.mu-news-item a {
	display: block;
	width: 9.5rem;
	margin: 0 auto;
	margin-top: 2rem;
	font-size: .9rem;
	padding: .5rem;
}

.mu-news-container {
	margin-bottom: 3rem;
}

.container.mu-building-description-container {
	padding-top: 8rem;
	padding-bottom: 5rem;
}

img.mu-building-thumbnail {
	width: 100%;
	margin-bottom: 5rem;
}

.mu-building-desc {
	margin-top: 4rem;
	font-weight: 300;
}

.mu-building-features-container {
	background: #F1F1F1;
	padding-top: 5rem;
	padding-bottom: 3rem;
}

.mu-building-features-icons {
	display: flex;
	justify-content: space-between;
	margin-top: 4rem;
}

.mu-building-features-icons--item {
	margin-bottom: 2rem;
}

.mu-building-features-icons--item img {
	margin: 0 auto;
	display: block;
	max-width: 5rem;
	max-height: 5rem;
}

.mu-building-features-icons--item p {
	text-align: center;
	margin-top: 2rem;
	font-size: 1.4rem;
	font-family: 'Abhaya Libre', serif;
}

.mu-building-gallery {
	display: flex;
}

.mu-building-gallery--active-item {
	width: 100%;
}

.mu-building-gallery--items {
	width: 28%;
	padding-left: .7rem;
}

.mu-building-gallery--items img {
	margin: 0 .2rem .6rem .2rem;
	width: 6.5rem;
	height: 6.5rem;
	cursor: pointer;
}

.mu-building-map-container {
	background: #F1F1F1;
	padding-top: 4rem;
	margin-top: 5rem;
}

.mu-building-map {
	margin-top: 2rem;
}

.mu-contact-person--item {
	width: 100%;
	display: flex;
	margin-top: 3rem;
}

.mu-contact-person--item img {
	max-width: 30%;
	width: 30%;
}

.mu-contact-person-item--content {
		display: flex;
		flex-wrap: wrap;
		flex-flow: column;
		justify-content: center;
		padding: 0 3rem;
		line-height: 1.1;
}

.mu-contact-person-item--content p {
		width: 100%;
}

p.mu-contact-person-item-content--name {
		font-family: 'Abhaya Libre', serif;
		font-size: 2rem;
		margin-bottom: .5rem;
}

p.mu-contact-person-item-content--role {
		font-family: 'Roboto Condensed', sans-serif;
		text-transform: uppercase;
		font-weight: 300;
}

p.mu-contact-person-item-content--phone {
		margin-bottom: .5rem;
		font-weight: 300;
}

p.mu-contact-person-item-content--mail {
		margin-bottom: 0;
		font-weight: 300;
}

p.mu-contact-person-item-content--phone:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-phone.png');
	width: 1rem;
	height: 1.1rem;
	content: ' ';
	display: inline-block;
	margin-right: 1rem;
	float: left;
}

p.mu-contact-person-item-content--phone.mu-contact-person-item-content--phone-white:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-phone-white.png')!important; 
}

p.mu-contact-person-item-content--mail:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-mail.png');
	width: 1rem;
	height: .85rem;
	content: ' ';
	display: inline-block;
	margin-right: 1rem;
	float: left;
	margin-top: .3rem;
}

p.mu-contact-person-item-content--mail.mu-contact-person-item-content--mail-white:before {
	background-image: url('/wp-content/themes/muchowiecka/inc/assets/img/mu-icon-mail-white.png')!important;
}

.mu-building-details-contact-content {
	padding-top: 5rem;
	padding-bottom: 5rem;
}

.wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output {
	margin-top: 5rem;
	color: #fff;
}

.mu-dark-content select option {
	color: #fff!important;
	background: #000!important;
}

.mu-building-details-info {
	margin-top: 4rem;
	font-weight: 300;
}

.mu-building-details-info a.mu-primary-btn {
	font-weight: 400;
	margin-top: 2rem;
}

.mu-about-partners--carousel img {
	padding: .5rem;
	/*padding: 2rem;*/
	max-height: 9rem;
	width: auto!important;
	margin: 0 auto!important;
	display: block!important;
}

.mu-building-gallery--active-item img {
	width: 100%;
}

img.mu-home-about-content--image {
	width: 28rem;
	margin-top: -10rem;
	margin-left: auto;
	margin-right: auto;
	display: block;
}

.mu-homepage-header {
	padding: 0 8rem;
	color: #fff;
}

.mu-homepage-header h1 {
	padding: 0;
	font-weight: 300;
	margin-bottom: 4rem;
}

a.mu-homepage-header--btn {
	font-family: 'Oswald', sans-serif;
	text-transform: uppercase;
}

.mu-homepage-header--subtitle {
	font-family: 'Oswald', sans-serif;
	font-size: 1.3rem;
}

.mu-homepage-header:before {
	display: block;
	content: ' ';
	background: #fff;
	width: .1rem;
	height: 32rem;
	margin-left: -2rem;
	position: absolute;
}

a.mu-homepage-header--btn {
	color: #fff;
	text-decoration: none;
	transition: .2s linear;
}

a.mu-homepage-header--btn i {
	margin-left: .5rem;
	transition: .2s linear;
}

a.mu-homepage-header--btn:hover i {
	margin-left: 1rem;
	transition: .2s linear;
}

a.mu-homepage-header--btn:hover {
	opacity: .8;
}

.mu-homepage-our-buildings {
	padding-top: 8rem;
	padding-bottom: 20rem;
	color: #fff;
	font-weight: 300;
}

.progress {
	overflow: visible;
	margin-bottom: 18px;
	height: 13px;
	padding: .2rem;
	border-radius: 0;
	background-color: rgb(255 255 255 / 62%);
}
.progress .progress-bar {
	background-color: #ffffff;
	position: relative;
	border-radius: 0px;
}
.progress .progress-bar span {
	background-color: #ba9778;
	position: absolute;
	bottom: 13px;
	font-size: 11px;
	line-height: 10px;
	padding: 4px 3px 4px 4px;
	right: -1.4em;
	border-radius: 2px;
}
.progress .progress-bar span:after {
	top: 100%;
	left: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(255, 255, 255, 0);
	border-bottom-color: #ba9778;
	border-width: 5px;
	margin-left: -5px;
	transform: rotate(180deg);
}

.mu-progress-item label {
	font-size: .85rem;
	font-weight: 400;
}

.mu-homepage-under-progress-counters {
	background: #fff;
	color: #333;
}

.mu-homepage-under-progress-counters:before {
	background: #fff;
	content: ' ';
	width: 60rem;
	height: 13rem;
	position: absolute;
	margin-left: -60rem;
	z-index: 1;
	margin-top: -3rem;
}

.mu-homepage-under-progress-counters {
	display: flex;
	justify-content: space-between;
	padding-top: 3rem;
	padding-bottom: 9rem;
	margin-top: 5rem;
}

.mu-under-progress-counter {
	display: flex;
	flex-wrap: wrap;
}

.mu-homepage-under-progress-counters-container {
	margin-top: -18rem;
}

span.mu-under-progress-counter--count {
	font-weight: 600;
	font-size: 3.4rem;
	flex: 1 1 100%;
	color: #c49a6c;
	font-family: 'Abhaya Libre', serif;
	margin-bottom: -.3rem;
}

span.mu-under-progress-counter--title {
	font-weight: 400;
	font-family: 'Oswald', sans-serif;
	flex: 1 1 100%;
}

span.mu-under-progress-counter--object {
	font-size: .9rem;
	margin-top: .3rem;
}

.mu-homepage-contact-content {
	background: #6d6e71;
	padding-top: 5rem;
	padding-bottom: 5rem;
}

.mu-contact-content-container--column.mu-light-content textarea {
	height: 7rem;
}

.mu-contact-content-container--column.mu-light-content input[type="text"], .mu-contact-content-container--column.mu-light-content input[type="tel"] {
	margin-bottom: 1rem;
}

.mu-contact-content-container--column.mu-light-content input[type="email"] {
	margin-bottom: 1rem;
}

.mu-contact-content-container--column.mu-light-content textarea {
	margin-bottom: 1rem;
}

.mu-contact-content-container--column.mu-light-content input.wpcf7-form-control.wpcf7-submit.btn.btn-primary {
	width: 100%;
}

.mu-cta {
	background: #C49A6C;
	color: #fff;
	padding-top: 2em;
	padding-bottom: 2.5rem;
}

.mu-cta a {
	margin-top: 1rem;
	float: right;
}

span.mu-cta-slogan {
	font-size: 3rem;
}

.mu-photos {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
}

.mu-photos img {
	width: 16.66666666666%;
}

.mu-our-offer {
	padding-top: 6rem;
	padding-bottom: 6rem;
}

.mu-col-vertical-center {
	display: flex;
	flex-wrap: wrap;
	flex-flow: column;
	align-items: baseline;
	justify-content: center;
}

.mu-home-tabs a.mu-primary-btn {
	margin-top: .5rem;
	float: right;
}

.nav-tabs {
	border-bottom: none;
}

.mu-home-tabs .nav-tabs {
	display: flex;
	justify-content: space-between;
}

.mu-home-tabs .nav-tabs a {
	color: #c49a6c;
	text-transform: uppercase;
	font-size: 1.1rem;
	padding-bottom: 1rem;
	margin-top: 4rem;
	border-bottom: 3px solid #f5f5f5;
	width: 100%;
	display: block;
	transition: .2s linear;
}

.mu-home-tabs li {
	width: 25%;
}

.mu-home-tabs li a.active {
	border-bottom: 3px solid #b8b8b8;
}

.mu-home-tabs li a:hover {
	background: none;
	border-bottom: 3px solid #b8b8b8;
	transition: .2s linear;
}

.mu-home-tabs li a:focus {
	background: none;
}

.mu-home-tabs .tab-content {
	margin-top: 4rem;
	margin-bottom: 5rem;
}

.mu-home-tab-item img {
	width: 100%;
	margin-bottom: 2rem;
}

p.mu-home-tab-item--category {
	color: #c49a6c;
	text-transform: uppercase;
	font-weight: 600;
	font-size: .9rem;
}

p.mu-home-tab-item--size {
	font-family: 'Abhaya Libre', serif;
	font-size: 2rem;
	margin-bottom: 0;
}

p.mu-home-tab-item--zone, p.mu-home-tab-item--zone a {
	font-family: 'Abhaya Libre', serif;
	font-size: 2.3rem;
	color: #c49a6c;
	margin-bottom: .5rem;
	text-decoration: none;
	line-height: 1;
}

p.mu-home-tab-item--desc {
	font-size: .9rem;
	font-weight: 300;
}

.mu-home-tabs li a {
	padding-left: .5rem;
}

p.mu-heading.mu-heading-pre {
	margin-top: 2.5rem;
	margin-bottom: 1rem;
}

.mu-buildings-slider-container {
	padding-bottom: 5rem;
	border-bottom: 1px solid #f1f1f1;
}

.mu-buildings-slider-content {
	background: #f1f1f1;
	padding: 6rem 3rem;
}

.mu-buildings-slider-container img.mu-buildings-slider-container--slide-image {
	float: right;
	position: relative;
	margin-top: -42rem;
	width: 55%;
}

p.mu-buildings-slider-content-features--item {
	border-top: 1px solid #c2c2c2;
	padding: .4rem 0;
	margin-bottom: 0;
}

p.mu-buildings-slider-content-features--item:last-child {
	border-bottom: 1px solid #c2c2c2;
}

p.mu-buildings-slider-content-features--item img {
	margin-right: 1rem;
	width: 1.3rem;
	margin-top: .2rem;
	float: left;
}

p.mu-buildings-slider-content-features--item span {
	float: right;
}

p.mu-buildings-slider-content-features--item {
	text-transform: uppercase;
	font-weight: 600;
	font-size: .9rem;
	line-height: 2;
}

p.mu-buildings-slider-content-features--item span {
	font-weight: 300;
}

.mu-features-container {
	padding-top: 2rem;
	padding-bottom: 2rem;
}

.mu-features-boxes {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 3.5rem;
}

.mu-features-boxes--item {
	background: #f1f1f1;
	flex: 0 0 23%;
	margin-bottom: 2rem;
	display: flex;
	flex-wrap: wrap;
	padding: 1.5rem 2rem;
}

.mu-features-boxes--item img {
	width: 2.5rem;
	float: right;
	display: block;
	margin-left: auto;
}

p.mu-features-boxes-item--sub-title {
	color: #c49a6c;
	font-size: .9rem;
	margin-bottom: .4rem;
	font-family: 'Oswald', sans-serif;
	width: 100%;
	margin-top: 2rem;
}

.mu-features-boxes-item--title {
	font-size: 1.5rem;
	font-family: 'Abhaya Libre', serif;
	margin-bottom: .4rem;
	font-weight: 400;
}

p.mu-features-boxes-item--desc {
	font-size: 1rem;
	font-family: 'Oswald', sans-serif;
	font-weight: 300;
	margin-bottom: 0;
}

.mu-features-boxes--item:nth-child(even) {
	background: #c49a6c;
	color: #fff;
}

.mu-features-boxes--item:nth-child(even) p.mu-features-boxes-item--sub-title {
	color: #fff;
}

.mu-news-item-content--title a {
	font-size: inherit;
	font-family: inherit;
	margin: inherit;
	width: inherit;
	color: inherit;
}

.mu-news-item-content--title a:hover {
	text-decoration: none;
	opacity: .9;
}

p.mu-news-item-content--category-meta a {
	display: contents;
	float: left!important;
	color: #C49A6C;
	text-transform: uppercase;
	text-decoration: none;
}

.mu-single-meta a {
	color: inherit;
	font-family: inherit;
	font-size: inherit;
	text-transform: uppercase;
}

.mu-page-header:before {
	content: ' ';
	background:rgb(68 68 68 / 44%);
	width: 100%;
	height: inherit;
	position: absolute;
	z-index: -1;
}

.mu-our-buildings-item--image {
	width: 50%;
	min-height: 30rem;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

.mu-offer-item--thumbnail {
	height: 27.1rem;
	width: 100%;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

.slick-next {
	right: 2rem!important;
	top: 95%!important;
	z-index: 1;
}

button.slick-prev.slick-arrow {
	top: 95%;
	left: inherit;
	right: 5rem;
	z-index: 1;
}

.mu-buildings-slider-carousel button.slick-prev.slick-arrow {
	right: 31rem!important;
}

.mu-buildings-slider-carousel .slick-next {
	right: 27rem!important;
}

p.mu-features-boxes-item--title {
		line-height: 1;
}

.mu-contact-content-container--column span.wpcf7-list-item-label a {
		color: #fff;
		font-weight: 600;
}

.mu-building-details-info-desc h2 {
		margin-top: 0;
		font-weight: 400;
}

.wpcf7 form.sent .wpcf7-response-output {
		margin-top: 5rem;
		color: #fff;
		text-align: center;
}

.mu-homepage-contact-content .mu-contact-person-item--content {
		padding: 0;
}

.mu-homepage-contact-content .mu-contact-person--item {
		margin-top: 1rem;
}

img.much-footer-fb {
		width: 1.3rem;
		margin-top: -2rem;
		transition: .2s linear;
}

img.much-footer-fb:hover {
		background: #000000;
		transition: .2s linear;
}

.mu-building-gallery--active-item img {
	width: auto;
	max-height: 45em;
	margin: 0 auto;
}

.mu-building-gallery--active-item img {
	width: auto;
	max-height: 40rem;
	margin: 0 auto;
	display: block;
}

.mu-building-gallery--items {
	width: 100%;
	margin-top: 2rem;
}

.mu-building-gallery--items img {
	width: 7rem;
}

#slb_viewer_wrap .slb_theme_slb_default .slb_details {
	display: none!important;
}

span.slb_slideshow {
	display: none!important;
}

.mu-btn-right {
	float: right!important;
}

.mu-homepage-our-buildings a.mu-primary-btn {
	margin-top: .5rem;
}

.mu-contact-person--item img {
	margin-right: 1.5rem;
}

@media screen and (max-width: 1500px) {
	.mu-buildings-slider-container img.mu-buildings-slider-container--slide-image {
		margin-top: -33rem;
	}
}


@media screen and (max-width: 1200px) {
	.navbar-light .navbar-toggler {
		background: #C49A6C;
	}
	
	.navbar-collapse {
		background: rgb(196 154 108 / 90%);
	}
}

@media screen and (max-width: 1500px) {
	.mu-buildings-slider-container img.mu-buildings-slider-container--slide-image {
		margin-top: -29rem;
	}
}

@media screen and (max-width: 1050px) {
	.mu-our-buildings-item--content:nth-child(even) {
		padding-right: 8rem;
	}

	.mu-our-buildings-item--content:nth-child(odd) {
			padding-left: 8rem;
	}
}

@media screen and (max-width: 992px) {
	img.mu-home-about-content--image {
		/* margin-top: 3rem;
		margin-left: 0; */
		display: none;
	}

	.mu-home-tab-item {
			margin-bottom: 4rem;
	}

	.mu-buildings-slider-container img.mu-buildings-slider-container--slide-image {
			float: none;
			position: initial;
			width: 100%;
			margin-top: -30rem;
	}

	.mu-buildings-slider-content {
			background: rgb(241 241 241 / 96%);
	}

	.mu-buildings-slider-carousel button.slick-prev.slick-arrow {
			right: 7rem!important;
	}

	.mu-buildings-slider-carousel .slick-next {
			right: 4rem!important;
	}

	.mu-features-boxes--item {
			flex: 0 0 48%;
	}

	.mu-contact-content-container--column {
			width: 100%;
			margin-bottom: 2rem;
	}

	.mu-contact-person--item img {
			max-width: 10rem;
			width: 10rem;
			height: 10rem;
	}

	.mu-contact-content-container--column .wpcf7 input[type="tel"] {
			margin-bottom: 1rem;
	}

	.mu-our-offer-carousel {
			margin-top: 4rem;
	}
	p.mu-page-header--pagename {
		padding: 0;
	}
	.mu-page-header h1 {
		padding: 0;
	}
	.mu-abount-content-container--column {
		width: 100%;
	}

	.mu-about-content p.mu-heading {
			margin-bottom: -1rem;
	}

	.mu-about-bg {
			margin-top: 4rem;
	}
	.mu-offer-item--thumbnail {
		margin-bottom: 3rem;
	}

	.mu-offer-item-content--params {
			text-align: left;
	}
	.mu-building-gallery--items {
		width: 45%;
	}
	.mu-building-details-info-desc {
		margin-top: 2.5rem;
	}

	.mu-building-details-contact-content {
			padding-top: 2rem;
			padding-bottom: 3rem;
	}
	.mu-building-desc {
		margin-top: 1rem;
	}
	.container.mu-building-description-container {
			padding-top: 5rem;
			padding-bottom: 3rem;
	}
	.mu-about-content {
		padding-top: 3rem;
		padding-bottom: 3rem;
	}
	.mu-contact-content-container-column--data {
		margin-top: 2rem;
	}
	.mu-news-item {
		flex: 0 0 40%;
	}
	div#footer-widget .widget_media_image {
		text-align: center;
		margin-bottom: 1rem;
	}

	section#text-2 {
			text-align: center;
	}
}


@media screen and (max-width: 768px) {
	.mu-buildings-slider-container img.mu-buildings-slider-container--slide-image {
		margin-top: -20rem;
	}
	.mu-homepage-under-progress-counters-container {
		margin-top: 0;
	}
	.mu-homepage-our-buildings {
		padding-bottom: 5rem;
	}
	.mu-about-contact-data--container {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.mu-about-contact-data-container--column {
		width: 100%;
	}
	.mu-building-gallery--items {
		width: 100%;
		margin-top: 1rem;
		margin-left: 0;
		padding-left: 0;
	}
	.mu-building-gallery {
			display: block;
	}
	.mu-building-gallery--items img {
			margin: 0 .2rem .6rem 0rem;
	}
	.mu-our-buildings-item--content:nth-child(even) {
		padding-left: 3rem;
		padding-right: 3rem;
	}
	.mu-our-buildings-item--content:nth-child(odd) {
			padding-left: 3rem;
			padding-right: 3rem;
	}
	.mu-news-categories ul {
		flex-wrap: wrap;
		justify-content: center;
	}
	p {
		font-size: 1.2rem;
	}
	.mu-single-meta {
		font-size: 1rem;
	}
	.mu-light-content {
		font-size: 1.2rem;
	}
	.mu-homepage-our-buildings div {
			font-size: 1.2rem;
			margin-bottom: 1rem;
	}
	span.mu-under-progress-counter--title {
			font-size: 1.2rem;
	}
	span.mu-under-progress-counter--object {
			font-size: 1.2rem;
	}
	.mu-under-progress-counter {
			margin-bottom: 2rem;
	}
	.mu-home-tabs .nav-tabs a {
			font-size: 1.2rem;
	}
	p.mu-home-tab-item--category {
			font-size: 1rem;
	}
	p.mu-home-tab-item--desc {
			font-size: 1.2rem;
	}
	p.mu-buildings-slider-content-features--item {
			font-size: 1.2rem;
	}
	p.mu-buildings-slider-content-features--item img {
			margin-top: .5rem;
	}
	p.mu-buildings-slider-content-features--item span {
			font-size: 1.2rem;
	}
	a.mu-primary-btn {
		font-size: 1.2rem;
	}
	.mu-features-container {
			padding-top: 4rem;
	}
	p.mu-features-boxes-item--sub-title {
			font-size: 1.2rem;
	}
	p.mu-features-boxes-item--title {
			font-size: 2rem;
	}
	p.mu-features-boxes-item--desc {
			font-size: 1.2rem;
	}
	.mu-features-boxes--item {
			padding-bottom: 3rem;
	}
	.mu-contact-content-container--column span.wpcf7-list-item-label {
			font-size: 1rem;
	}
	.mu-contact-content-container--column.mu-light-content input.wpcf7-form-control.wpcf7-submit.btn.btn-primary {
			font-size: 1.2rem;
	}
	.mu-contact-content-container--column input, .mu-contact-content-container--column textarea, .mu-contact-content-container--column select {
			font-size: 1.2rem;
	}
	#footer-widget a.nav-link {
			font-size: 1.2rem;
	}
	.copyright a, footer#colophon a {
			font-size: 1.2rem;
	}
	.site-info {
			font-size: 1.2rem;
	}
	a.mu-homepage-header--btn {
		font-size: 1.2rem;
	}
	body:not(.theme-preset-active) #masthead .navbar-nav > li > a {
			font-size: 1.2rem;
	}
	p.mu-offer-item-content--categories {
		font-size: 1rem;
	}
	.mu-offer-item-content--desc {
			font-size: 1.2rem;
	}
	.mu-offer-item-content--params {
			font-size: 1.2rem;
	}
	.mu-building-details-info {
		font-size: 1.2rem;
	}
	.mu-news-categories {
		font-size: 1.2rem;
	}
	p.mu-news-item-content--category-meta {
			font-size: 1.1rem;
	}
	.mu-news-item-content--title {
			font-size: 1.6rem;
	}
	.mu-news-item a {
			width: 14.5rem;
			padding: 1rem;
	}
	.mu-btn-right {
		float: initial!important;
	}
}

@media screen and (max-width: 700px) {
	.mu-homepage-header {
		padding: 0 2rem;
	}
}

@media screen and (max-width: 600px) {
	.mu-homepage-under-progress-counters:before {
		display: none;
	}
	.mu-homepage-under-progress-counters {
		flex-wrap: wrap;
		justify-content: center;
	}
	.mu-under-progress-counter {
		justify-content: center;
	}

	span.mu-under-progress-counter--count {
			text-align: center;
	}

	span.mu-under-progress-counter--title {
			text-align: center;
	}
	div#footer-widget {
		padding: 3rem 0;
	}
	p.mu-page-header--pagename {
		padding: 0 2rem;
	}
	.mu-page-header h1 {
		padding: 0 2rem;
	}
	.mu-building-features-icons {
		flex-wrap: wrap;
		margin-top: 2rem;
	}
	.mu-building-features-icons--item {
			flex: 0 0 100%;
	}
	.mu-news-item {
		flex: 0 0 80%;
	}
}

@media screen and (max-width: 575px) { 
	span.mu-cta-slogan {
		text-align: center;
	}
	.mu-cta {
			text-align: center;
	}
	.mu-cta a {
			float: none;
			display: inline-block;
			width: auto;
			padding: 1rem 2rem;
	}
}

@media screen and (max-width: 550px) { 
	.mu-about-content {
		padding-top: 4rem;
		padding-bottom: 5rem;
	}
}

@media screen and (max-width: 500px) { 
	.mu-features-boxes--item {
		flex: 0 0 100%;
	}
	.mu-about-contact-data {
		padding: 2rem 1rem;
	}
}

@media screen and (max-width: 450px) { 
	.mu-home-tabs li {
		width: 50%;
	}
}

@media screen and (max-width: 430px) { 
	span.mu-cta-slogan {
		font-size: 2rem;
	}
	.mu-cta a.mu-primary-btn {
			margin-top: 0;
			padding-right: 2.5rem;
	}
}

@media screen and (max-width: 420px) { 
	.mu-our-buildings-item--content:nth-child(even) {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.mu-our-buildings-item--content:nth-child(odd) {
			padding-left: 2rem;
			padding-right: 2rem;
	}
}

@media screen and (max-width: 400px) { 
	.mu-under-progress-counter {
		margin-bottom: 2rem;
	}
	.mu-contact-person-item--content {
		padding: 0 1rem;
	} 
	.navbar-brand {
		width: 13rem;
	}
	.navbar-brand:after {
			display: none;
	}
	.mu-btns-container {
		justify-content: space-around;
	}
	.mu-our-buildings-item--image {
		display: none;
	}
	.mu-our-buildings-item--content {
			width: 100%;
			padding-bottom: 4rem;
			margin-top: 4rem;
			border-bottom: 1px solid #eee;
	}
}

.ma-icon {
	width: 20px;
	height: 20px;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	display: inline-block;
}
.ma-fb {
	background-image: url(icon-facebook.svg);
}



.oferta_wynajmu-template-default .ma-fb {
	background-image: url(icon-facebook_black.svg);
}
.oferta_wynajmu-template-default header#masthead {
	position: relative;
}
.oferta_wynajmu-template-default #masthead .navbar-nav > li > a {
	color: #191919!important;
}

.navbar-nav {
	flex-wrap: nowrap !important;
    align-content: center !important;
    align-items: center !important;
}

.navbar-nav>li {
	width: auto !important;
}

.navbar-nav>li>a {
	margin: 0 .3rem !important;
}